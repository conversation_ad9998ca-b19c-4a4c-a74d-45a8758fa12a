import {CommonModule} from '@angular/common';
import {NgModule, inject} from '@angular/core';
import {RouterModule, ActivatedRouteSnapshot} from '@angular/router';
import {CanDeactivateDetailGuard, createResolveData, SharedModule, DataService} from '@vendure/admin-ui/core';
import {ComponentSharedModule} from './component-shared.module';
import {EcommerceCommonPipeSharedModule} from './ecommerce-common-pipe-shared.module';

import {AnnouncementComponent} from './components/announcement/announcement.component';
import {AnnouncementModule} from './components/announcement/announcement.module';
import {CouponsDetailComponent} from './components/coupons-detail/coupons-detail.component';
import {CouponsDetailModule} from './components/coupons-detail/coupons-detail.module';
import {CouponsListComponent} from './components/coupons-list/coupons-list.component';
import {CouponsListModule} from './components/coupons-list/coupons-list.module';
import {CouponPackageListComponent} from './components/coupon-package/coupon-package-list/coupon-package-list.component';
import {CouponPackageDetailComponent} from './components/coupon-package/coupon-package-detail/coupon-package-detail.component';
import {CouponsReceivedListComponent} from './components/coupons-received-list/coupons-received-list.component';
import {CouponsReceivedListModule} from './components/coupons-received-list/coupons-received-list.module';
import {CustomPageDetailComponent} from './components/custom-page-detail/custom-page-detail.component';
import {CustomPageDetailModule} from './components/custom-page-detail/custom-page-detail.module';
import {CustomPageListComponent} from './components/custom-page-list/custom-page-list.component';
import {CustomPageListModule} from './components/custom-page-list/custom-page-list.module';
import {CustomersListComponent} from './components/customers-list/customers-list.component';
import {CustomersListModule} from './components/customers-list/customers-list.module';
import {DistributorListComponent} from './components/distributior-list/distributor-list.component';
import {DistributorListModule} from './components/distributior-list/distributor-list.module';
import {DistributorCreateComponent} from './components/distributor-create/distributor-create.component';
import {DistributorCreateModule} from './components/distributor-create/distributor-create.module';
import {DistributorGroupListComponent} from './components/distributor-group/distributor-group-list/distributor-group-list.component';
import {DistributorGroupListModule} from './components/distributor-group/distributor-group-list/distributor-group-list.module';
import {DistributorGroupDetailComponent} from './components/distributor-group/distributor-group-detail/distributor-group-detail.component';
import {DistributorGroupDetailModule} from './components/distributor-group/distributor-group-detail/distributor-group-detail.module';
import {DistributorGroupResolver} from './components/distributor-group/distributor-group-detail/distributor-group-resolver';
import {FullGiftDetailComponent} from './components/full-gift-detail/full-gift-detail.component';
import {FullGiftDetailModule} from './components/full-gift-detail/full-gift-detail.module';
import {FullGiftResolver} from './components/full-gift-detail/full-gift-detail.resolver';
import {FullGiftListComponent} from './components/full-gift-list/full-gift-list.component';
import {FullGiftListModule} from './components/full-gift-list/full-gift-list.module';
// import {GiftDetailComponent} from './components/gift-detail/gift-detail.component';
import {GiftDetailModule} from './components/gift-detail/gift-detail.module';
// import {GiftResolver} from './components/gift-detail/gift-detail.resolver';
import {GiftListComponent} from './components/gift-list/gift-list.component';
import {GiftListModule} from './components/gift-list/gift-list.module';
import {HalfPriceDetailComponent} from './components/half-price-detail/half-price-detail.component';
import {HalfPriceDetailModule} from './components/half-price-detail/half-price-detail.module';
import {HalfPriceResolver} from './components/half-price-detail/half-price-resolver';
import {HalfPriceListComponent} from './components/half-price-list/half-price-list.component';
import {HalfPriceListModule} from './components/half-price-list/half-price-list.module';
import {MarkUpBuyDetailComponent} from './components/mark-up-buy-detail/mark-up-buy-detail.component';
import {MarkUpBuyDetailModule} from './components/mark-up-buy-detail/mark-up-buy-detail.module';
import {MarkUpBuyListComponent} from './components/mark-up-buy-list/mark-up-buy-list.component';
import {MarkUpBuyListModule} from './components/mark-up-buy-list/mark-up-buy-list.module';
import {MemberCardDetailComponent} from './components/member-card-detail/member-card-detail.component';
import {MemberCardDetailModule} from './components/member-card-detail/member-card-detail.module';
import {MemberCardResolver} from './components/member-card-detail/member-card-resolver';
import {MemberCardGetRecordListComponent} from './components/member-card-get-record-list/member-card-get-record-list.component';
import {MemberCardGetRecordListModule} from './components/member-card-get-record-list/member-card-get-record-list.module';
import {MemberCardGrantModule} from './components/member-card/member-card-grant/member-card-grant.module';
import {MemberCardGrantComponent} from './components/member-card/member-card-grant/member-card-grant.component';
import {MemberCardListComponent} from './components/member-card-list/member-card-list.component';
import {MemberCardListModule} from './components/member-card-list/member-card-list.module';
import {MemberCardRefundRecordListComponent} from './components/member-card-refund-record-list/member-card-refund-record-list.component';
import {MemberCardRefundRecordListModule} from './components/member-card-refund-record-list/member-card-refund-record-list.module';
import {GiftCardListComponent} from './components/gift-card/gift-card-list/gift-card-list.component';
import {GiftCardListModule} from './components/gift-card/gift-card-list/gift-card-list.module';
import {GiftCardDetailComponent} from './components/gift-card/gift-card-detail/gift-card-detail.component';
import {GiftCardDetailModule} from './components/gift-card/gift-card-detail/gift-card-detail.module';
import {GiftCardResolver} from './components/gift-card/gift-card-detail/gift-card-resolver';
import {GiftCardGetListModule} from './components/gift-card/gift-card-get-list/gift-card-get-list.module';
import {GiftCardGetListComponent} from './components/gift-card/gift-card-get-list/gift-card-get-list.component';
import {GiftCardRefundListModule} from './components/gift-card/gift-card-refund-list/gift-card-refund-list.module';
import {GiftCardRefundListComponent} from './components/gift-card/gift-card-refund-list/gift-card-refund-list.component';
import {OperationPlanListModule} from './components/operation-plan/operation-plan-list/operation-plan-list.module';
import {OperationPlanListComponent} from './components/operation-plan/operation-plan-list/operation-plan-list.component';
import {OperationPlanDetailModule} from './components/operation-plan/operation-plan-detail/operation-plan-detail.module';
import {OperationPlanDetailComponent} from './components/operation-plan/operation-plan-detail/operation-plan-detail.component';
import {OrderDetailComponent} from './components/order-detail/order-detail.component';
import {OrderDetailModule} from './components/order-detail/order-detail.module';
import {OrderListComponent} from './components/order-list/order-list.component';
import {OrderListModule} from './components/order-list/order-list.module';
import {AfterSaleListModule} from './components/after-sale/after-sale-list/after-sale-list.module';
import {AfterSaleListComponent} from './components/after-sale/after-sale-list/after-sale-list.component';
import {AfterSaleDetailsModule} from './components/after-sale/after-sale-detail/after-sale-detail.module';
import {AfterSaleDetailComponent} from './components/after-sale/after-sale-detail/after-sale-detail.component';
import {SubscriptionPlanModule} from './components/subscription/subscription-plan/subscription-plan.module';
import {SubscriptionPlanComponent} from './components/subscription/subscription-plan/subscription-plan.component';
import {ChannelAddressListModule} from './components/channel-address/channel-address-list/channel-address-list.module';
import {ChannelAddressListComponent} from './components/channel-address/channel-address-list/channel-address-list.component';

import {ProductDetailComponent} from './components/product-detail/product-detail.component';
import {ProductDetailModule} from './components/product-detail/product-detail.module';
import {ProductListComponent} from './components/product-list/product-list.component';
import {ProductListModule} from './components/product-list/product-list.module';
import {ProductVariantsEditorComponent} from './components/product-variants-editor/product-variants-editor.component';
import {ProductVariantsEditorModule} from './components/product-variants-editor/product-variants-editor.module';
import {FacetListModule} from './components/facet-list/facet-list.module';
import {FacetListComponent} from './components/facet-list/facet-list.component';
import {FacetDetailModule} from './components/facet-detail/facet-detail.module';
import {FacetDetailComponent} from './components/facet-detail/facet-detail.component';
import {CollectionListComponent} from './components/collection-list/collection-list.component';
import {CollectionListModule} from './components/collection-list/collection-list.module';
import {CollectionDetailComponent} from './components/collection-detail/collection-detail.component';
import {CollectionDetailModule} from './components/collection-detail/collection-detail.module';
import {AssetListModule} from './components/asset-list/asset-list.module';
import {AssetListComponent} from './components/asset-list/asset-list.component';
import {AssetDetailModule} from './components/asset-detail/asset-detail.module';
import {AssetDetailComponent} from './components/asset-detail/asset-detail.component';

import {SearchHotWordsComponent} from './components/search-hot-words/search-hot-words.component';
import {SearchHotWordsFormModule} from './components/search-hot-words/search-hot-words.module';
import {Coupon} from './generated-shop-types';
import {MarkUpBuyResolver} from './components/mark-up-buy-detail/mark-up-buy-resolver-resolver';
import {ProductResolver} from './components/product-detail/product-resolver';
import {ProductVariantsResolver} from './providers/product-variants-resolver';
import {CustomerDetailComponent} from './components/customer-detail/customer-detail.component';
import {CustomerDetailModule} from './components/customer-detail/customer-detail.module';
import {CustomerGroupListModule} from './components/customer-group-list/customer-group-list.module';
import {CustomerGroupListComponent} from './components/customer-group-list/customer-group-list.component';
import {CustomerGroupDetailModule} from './components/customer-group-detail/customer-group-detail.module';
import {CustomerGroupDetailComponent} from './components/customer-group-detail/customer-group-detail.component';
import {CustomerDivisionListModule} from './components/customer/customer-division-list/customer-division-list.module';
import {CustomerDivisionListComponent} from './components/customer/customer-division-list/customer-division-list.component';
import {CustomerDivisionDetailModule} from './components/customer/customer-division-detail/customer-division-detail.module';
import {CustomerDivisionDetailComponent} from './components/customer/customer-division-detail/customer-division-detail.component';

import {GeneralSettingComponent} from './components/general-setting/general-setting.component';
import {GeneralSettingModule} from './components/general-setting/general-setting.module';
import {PaymentMethodListComponent} from './components/payment-method-list/payment-method-list.component';
import {PaymentMethodListModule} from './components/payment-method-list/payment-method-list.module';
import {PaymentMethodResolver} from './components/payment-method-detail/payment-method-resolver';
import {PaymentMethodDetailComponent} from './components/payment-method-detail/payment-method-detail.component';
import {PaymentMethodDetailModule} from './components/payment-method-detail/payment-method-detail.module';

import {RecommendManagementComponent} from './components/recommend-management/recommend-management.component';
import {RecommendManagementModule} from './components/recommend-management/recommend-management.module';

import {DataBoardModule} from './components/data-statistics/data-board/data-board.module';
import {DataBoardComponent} from './components/data-statistics/data-board/data-board.component';

import {CollectionResolver} from './components/collection-detail/collection-resolver';
import {CustomerResolver} from './components/customer-detail/customer-resolver';
import {CustomerDivisionResolver} from './components/customer/customer-division-detail/customer-division-resolver';
import {OperationPlanResolver} from './components/operation-plan/operation-plan-detail/operation-plan-resolver';
import {OrderResolver} from './providers/order-resolver';

import {of, map} from 'rxjs';
import {
  AssetDetailQueryDocument,
  GetFacetDetailDocument,
  GetCustomerGroupDetailDocument,
  GetChannelDetailDocument,
} from '@vendure/admin-ui/core';
import {ProductInsightsModule} from './components/data-statistics/product-insights/product-insights.module';
import {ProductInsightsComponent} from './components/data-statistics/product-insights/product-insights.component';
import {CustomerTradeComponent} from './components/data-statistics/customer-trade/customer-trade.component';
import {CustomerTradeModule} from './components/data-statistics/customer-trade/customer-trade.module';
import {PageStatisticsComponent} from './components/data-statistics/page-statistics/page-statistics.component';
import {PageStatisticsModule} from './components/data-statistics/page-statistics/page-statistics.module';
import {ChannelListModule} from './components/channel/channel-list/channel-list.module';
import {ChannelListComponent} from './components/channel/channel-list/channel-list.component';
import {ChannelDetailModule} from './components/channel/channel-detail/channel-detail.module';
import {ChannelDetailComponent} from './components/channel/channel-detail/channel-detail.component';
import {PackageDiscountListModule} from './components/package-discount/package-discount-list/package-discount-list.module';
import {PackageDiscountListComponent} from './components/package-discount/package-discount-list/package-discount-list.component';
import {PackageDiscountDetailModule} from './components/package-discount/package-discount-detail/package-discount-detail.module';
import {PackageDiscountDetailComponent} from './components/package-discount/package-discount-detail/package-discount-detail.component';
import {PackageDiscountResolver} from './components/package-discount/package-discount-detail/package-discount.resolver';
import {MemberPriceListModule} from './components/member-price-activity/member-price-list/member-price-list.module';
import {MemberPriceListComponent} from './components/member-price-activity/member-price-list/member-price-list.component';
import {MemberPriceDetailModule} from './components/member-price-activity/member-price-detail/member-price-detail.module';
import {MemberPriceDetailComponent} from './components/member-price-activity/member-price-detail/member-price-detail.component';
import {MemberPriceResolver} from './components/member-price-activity/member-price-detail/member-price-resolver';

import {OptionalGiftsListComponent} from './components/optional-gifts/optional-gifts-list/optional-gifts-list.component';
import {OptionalGiftsDetailComponent} from './components/optional-gifts/optional-gifts-detail/optional-gifts-detail.component';
import {GiftSelectDialogComponent} from './components/gifts/gift-select-dialog/gift-select-dialog.component';
import {OptionalGiftsResolver} from './components/optional-gifts/optional-gifts-detail/optional-gifts-detail.resolver';
import {ActivityProductSelectorModule} from './components/products/activity-product-selector/activity-product-selector.module';
import {PromotionDialogModule} from './components/promotion-dialog/promotion-dialog.module';

import {PayGiftListComponent} from './components/pay-gift/pay-gift-list/pay-gift-list.component';
import {PayGiftDetailComponent} from './components/pay-gift/pay-gift-detail/pay-gift-detail.component';
import {PaymentRewardResolver} from './components/pay-gift/pay-gift-detail/pay-gift.resolver';
import {CouponSelectorModule} from './components/coupon/coupon-selector/coupon-selector.module';
import {CouponPackageResolver} from './components/coupon-package/resolvers/coupon-package-resolver';
import {NewCustomerPackageResolver} from './components/coupon-package/resolvers/new-customer-package-resolver';
import {ExclusionGroupDetailModule} from './components/exclusion-group/exclusion-group-detail/exclusion-group-detail.module';
import {ExclusionGroupDetailComponent} from './components/exclusion-group/exclusion-group-detail/exclusion-group-detail.component';
import {ExclusionGroupListModule} from './components/exclusion-group/exclusion-group-list/exclusion-group-list.module';
import {ExclusionGroupListComponent} from './components/exclusion-group/exclusion-group-list/exclusion-group-list.component';
import {ExclusionGroupResolver} from './components/exclusion-group/exclusion-group-detail/exclusion-group-resolver';
import {PointsProductListModule} from './components/points-shop/points-product-list/points-product-list.module';
import {PointsProductListComponent} from './components/points-shop/points-product-list/points-product-list.component';
import {PointsConfigComponent} from './components/points-shop/points-config/points-config.component';
import {PointsConfigModule} from './components/points-shop/points-config/points-config.module';
import {PointsProductDetailComponent} from './components/points-shop/points-product-detail/points-product-detail.component';
import {PointsProductDetailModule} from './components/points-shop/points-product-detail/points-product-detail.module';
import {PointsProductResolver} from './components/points-shop/points-product-detail/points-product-resolver';
import {CheckInStatisticsComponent} from './components/check-in-management/check-in-statistics/check-in-statistics.component';
import {CheckInStatisticsModule} from './components/check-in-management/check-in-statistics/check-in-statistics.module';
import {CheckInConfigComponent} from './components/check-in-management/check-in-config/check-in-config.component';
import {CheckInConfigModule} from './components/check-in-management/check-in-config/check-in-config.module';
import {CheckInCustomerListComponent} from './components/check-in-management/check-in-customer-list/check-in-customer-list.component';
import {CheckInCustomerListModule} from './components/check-in-management/check-in-customer-list/check-in-customer-list.module';
import {ExportTaskListComponent} from './components/export-task-list/export-task-list.component';
import {ExportTaskListModule} from './components/export-task-list/export-task-list.module';
import {BlindBoxModule} from './components/blind-box/blind-box.module';
import {BlindBoxConfigComponent} from './components/blind-box/blind-box-config/blind-box-config.component';
import {BlindBoxConfigResolver} from './components/blind-box/blind-box-config/blind-box-config-resolver';
import {BlindBoxListComponent} from './components/blind-box/blind-box-list/blind-box-list.component';
import {BlindBoxDetailComponent} from './components/blind-box/blind-box-detail/blind-box-detail.component';
import {BlindBoxResolver} from './components/blind-box/blind-box-detail/blind-box-resolver';
import {BlindBoxActivityListComponent} from './components/blind-box/blind-box-activity-list/blind-box-activity-list.component';
import {BlindBoxActivityResolver} from './components/blind-box/blind-box-activity-detail/blind-box-activity-resolver';
import {BlindBoxActivityDetailComponent} from './components/blind-box/blind-box-activity-detail/blind-box-activity-detail.component';
import {BlindBoxOpenRecordListComponent} from './components/blind-box/blind-box-open-record-list/blind-box-open-record-list.component';
import {BlindBoxOpenRecordResolver} from './components/blind-box/blind-box-open-record-detail/blind-box-open-record-resolver';
import {BlindBoxOpenRecordDetailComponent} from './components/blind-box/blind-box-open-record-detail/blind-box-open-record-detail.component';
import {BlindBoxStatisticsComponent} from './components/blind-box/blind-box-statistics/blind-box-statistics.component';
import {FloatWindowConfigModule} from './components/float-window-config/float-window-config.module';
import {FloatWindowConfigComponent} from './components/float-window-config/float-window-config.component';
import {FloatingWindowConfigResolver} from './components/float-window-config/float-window-config-resolver';
import {CouponResolver} from './components/coupons-detail/coupon-resolver';
import {WishNoteModule} from './components/store-notes/wish-note.module';
import {ChannelPostListComponent} from './components/store-notes/channel-post-list/channel-post-list.component';
import {ChannelPostDetailComponent} from './components/store-notes/channel-post-detail/channel-post-detail.component';
import {CustomerPostListComponent} from './components/store-notes/customer-post-list/customer-post-list.component';
import {PostDetailComponent} from './components/store-notes/post-detail/post-detail.component';
import {ForumActivityDetailComponent} from './components/store-notes/forum-activity-detail/forum-activity-detail.component';
import {ForumActivityListComponent} from './components/store-notes/forum-activity-list/forum-activity-list.component';
import {ForumTagDetailComponent} from './components/store-notes/forum-tag-detail/forum-tag-detail.component';
import {ForumTagListComponent} from './components/store-notes/forum-tag-list/forum-tag-list.component';
import {ForumActivityRankComponent} from './components/store-notes/forum-activity-rank/forum-activity-rank.component';
import {PostResolver} from './components/store-notes/post-detail/post-resolver';
import {ForumTagResolver} from './components/store-notes/forum-tag-detail/forum-tag-resolver';
import {ForumActivityResolver} from './components/store-notes/forum-activity-detail/forum-activity-resolver';
import {ChannelPostResolver} from './components/store-notes/channel-post-detail/channel-post-resolver';
import {PostCommentListComponent} from './components/store-notes/post-comment-list/post-comment-list.component';
import {PostCommentDetailComponent} from './components/store-notes/post-comment-detail/post-comment-detail.component';
import {PostCommentResolver} from './components/store-notes/post-comment-detail/post-comment-resolver';
import {ForumCustomerListComponent} from './components/store-notes/forum-customer-list/forum-customer-list.component';
import {ShopManagementModule} from './components/shop-management/shop-management.module';
import {ShareSettingsComponent} from './components/shop-management/share-settings/share-settings.component';
import {PersonalCenterSettingsComponent} from './components/personal-center-settings/personal-center-settings.component';
import {PersonalCenterSettingsModule} from './components/personal-center-settings/personal-center-settings.module';
import {ActivityCountdownDetailComponent} from './components/activity-countdown/activity-countdown-detail/activity-countdown-detail.component';
import {ActivityCountdownModule} from './components/activity-countdown/activity-countdown.module';
import {ActivityCountdownResolver} from './components/activity-countdown/activity-countdown-detail/activity-countdown-resolver';
import {ActivityCountdownListComponent} from './components/activity-countdown/activity-countdown-list/activity-countdown-list.component';
import {ShoppingCreditsGrantDetailComponent} from './components/shopping-credits/shopping-credits-grant-detail/shopping-credits-grant-detail.component';
import {ShoppingCreditsGrantListComponent} from './components/shopping-credits/shopping-credits-grant-list/shopping-credits-grant-list.component';
import {ShoppingCreditsGrantResolver} from './components/shopping-credits/shopping-credits-grant-detail/shopping-credits-grant.resolver';
import {ShoppingCreditsUseDetailComponent} from './components/shopping-credits/shopping-credits-use-detail/shopping-credits-use-detail.component';
import {ShoppingCreditsUseListComponent} from './components/shopping-credits/shopping-credits-use-list/shopping-credits-use-list.component';
import {ShoppingCreditsUseResolver} from './components/shopping-credits/shopping-credits-use-detail/shopping-credits-use.resolver';
import {ShoppingCreditsModule} from './components/shopping-credits/shopping-credits.module';
import {ShoppingCreditsConfigComponent} from './components/shopping-credits/shopping-credits-config/shopping-credits-config.component';
import {ExportTaskListResolver} from './components/export-task-list/export-task-list.resolver';
import {MemberLevelModule} from './components/member-level/member-level.module';
import {MemberLevelListComponent} from './components/member-level/member-level-list/member-level-list.component';
import {MemberLevelResolver} from './components/member-level/member-level-detail/member-level-resolver';
// import {
//   CanActivateListGuard,
//   ListResolver,
// } from './providers/routing/canActivateListGuard';

const COMPS = [CouponPackageDetailComponent, CouponPackageListComponent];

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    ComponentSharedModule,
    EcommerceCommonPipeSharedModule,
    CouponSelectorModule,
    ActivityProductSelectorModule,
    PromotionDialogModule,
    //如果是模块，添加路由的同时记得把模块也添加进来
    ProductListModule,
    ProductDetailModule,
    ProductVariantsEditorModule,
    FacetListModule,
    FacetDetailModule,
    CollectionListModule,
    CollectionDetailModule,
    AssetListModule,
    AssetDetailModule,
    OrderListModule,
    OrderDetailModule,
    AfterSaleListModule,
    AfterSaleDetailsModule,
    SubscriptionPlanModule,
    ChannelAddressListModule,
    CustomPageListModule,
    CustomPageDetailModule,
    CustomerDivisionListModule,
    CustomerDivisionDetailModule,
    SearchHotWordsFormModule,
    AnnouncementModule,
    MemberCardListModule,
    MemberCardDetailModule,
    MemberCardGetRecordListModule,
    MemberCardGrantModule,
    MemberCardRefundRecordListModule,
    GiftCardListModule,
    GiftCardDetailModule,
    GiftCardGetListModule,
    GiftCardRefundListModule,
    OperationPlanListModule,
    OperationPlanDetailModule,
    CouponsListModule,
    CouponsReceivedListModule,
    CouponsDetailModule,
    MarkUpBuyListModule,
    MarkUpBuyDetailModule,
    DistributorListModule,
    DistributorCreateModule,
    DistributorGroupListModule,
    DistributorGroupDetailModule,
    HalfPriceListModule,
    HalfPriceDetailModule,
    CustomersListModule,
    CustomerGroupListModule,
    CustomerGroupDetailModule,
    GiftListModule,
    GiftDetailModule,
    FullGiftListModule,
    FullGiftDetailModule,
    CustomerDetailModule,
    GeneralSettingModule,
    PaymentMethodListModule,
    PaymentMethodDetailModule,
    RecommendManagementModule,
    DataBoardModule,
    ProductInsightsModule,
    CustomerTradeModule,
    PageStatisticsModule,
    ChannelListModule,
    ChannelDetailModule,
    PackageDiscountListModule,
    PackageDiscountDetailModule,
    MemberPriceListModule,
    MemberPriceDetailModule,
    ExclusionGroupListModule,
    ExclusionGroupDetailModule,
    PointsProductListModule,
    PointsProductDetailModule,
    PointsConfigModule,
    CheckInStatisticsModule,
    CheckInConfigModule,
    CheckInCustomerListModule,
    ExportTaskListModule,
    BlindBoxModule,
    FloatWindowConfigModule,
    WishNoteModule,
    ShopManagementModule,
    PersonalCenterSettingsModule,
    ActivityCountdownModule,
    ShoppingCreditsModule,
    MemberLevelModule,
    // RouterOutModule,
    RouterModule.forChild([
      // {
      //   path: 'ecommerce',
      //   pathMatch: 'full',
      // },
      {
        path: 'product-list',
        // canActivate: [CanActivateListGuard],
        // resolve: {list: ListResolver},
        component: ProductListComponent,
        data: {breadcrumb: '商品列表'},
      },
      {
        path: 'product-list/:id',
        component: ProductDetailComponent,
        resolve: createResolveData(ProductResolver),
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          breadcrumb: detailBreadcrumb('商品详情', '商品列表', 'product-list'),
        },
      },
      {
        path: 'product-list/:id/manage-variants',
        component: ProductVariantsEditorComponent,
        resolve: createResolveData(ProductVariantsResolver),
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          breadcrumb(_data: () => void, params: {id: string}) {
            return [
              {
                label: '商品列表',
                link: ['extensions/ecommerce/product-list'],
              },
              {
                label: '商品详情',
                link: ['extensions/ecommerce/product-list', params.id],
              },
              {
                label: '规格管理',
                link: ['./'],
              },
            ];
          },
        },
      },
      {
        path: 'collection-list',
        pathMatch: 'full',
        component: CollectionListComponent,
        data: {breadcrumb: '商品系列'},
      },
      {
        path: 'collection-list/:id',
        component: CollectionDetailComponent,
        resolve: createResolveData(CollectionResolver),
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          breadcrumb: detailBreadcrumb('系列详情', '商品列表', 'collection-list'),
        },
      },
      {
        path: 'asset-list',
        pathMatch: 'full',
        component: AssetListComponent,
        data: {breadcrumb: '资源列表'},
      },
      {
        path: 'asset-list/:id',
        pathMatch: 'full',
        component: AssetDetailComponent,
        resolve: {
          detail: (route: ActivatedRouteSnapshot) => {
            return inject(DataService)
              .query(AssetDetailQueryDocument, {id: route.paramMap.get('id') ?? ''})
              .mapStream(data => ({entity: of(data.asset)}));
          },
        },
        data: {breadcrumb: detailBreadcrumb('资源详情', '资源列表', 'asset-list')},
      },
      {
        path: 'facet-list',
        pathMatch: 'full',
        component: FacetListComponent,
        data: {breadcrumb: '商品特征'},
      },
      {
        path: 'facet-list/:id',
        pathMatch: 'full',
        component: FacetDetailComponent,
        resolve: {
          detail: (route: ActivatedRouteSnapshot) => {
            return inject(DataService)
              .query(GetFacetDetailDocument, {id: route.paramMap.get('id') ?? ''})
              .mapStream(data => ({entity: of(data.facet)}));
          },
        },
        data: {breadcrumb: detailBreadcrumb('特征详情', '商品特征', 'facet-list')},
      },
      {
        path: 'order-list',
        pathMatch: 'full',
        component: OrderListComponent,
        data: {breadcrumb: '订单列表'},
      },
      {
        path: 'order-list/:id',
        resolve: createResolveData(OrderResolver),
        component: OrderDetailComponent,
        data: {
          breadcrumb: detailBreadcrumb('订单详情', '订单列表', 'order-list'),
        },
      },
      {
        path: 'export-task-list/:exportType',
        component: ExportTaskListComponent,
        resolve: createResolveData(ExportTaskListResolver),
        data: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          breadcrumb: (_data: any, params: {exportType: string}) => {
            const {exportType} = params;
            let rootCrumb: {label: string; link: string[]};
            let lastLabel = '导出记录列表';
            if (exportType === 'order') {
              rootCrumb = {label: '订单列表', link: ['extensions/ecommerce/order-list']};
            } else {
              //
              rootCrumb = {label: '盲盒开盒记录列表', link: ['extensions/ecommerce/blind-box-open-record-list']};
              if (exportType === 'blindBoxStatistics') {
                lastLabel = '盲盒统计数据导出记录列表';
              }
            }
            return [
              rootCrumb,
              {
                label: lastLabel,
                link: [''],
              },
            ];
          },
        },
      },
      {
        path: 'after-sale-list',
        pathMatch: 'full',
        component: AfterSaleListComponent,
        data: {breadcrumb: '售后列表'},
      },
      {
        path: 'after-sale-list/:id',
        pathMatch: 'full',
        component: AfterSaleDetailComponent,
        data: {
          breadcrumb: () => {
            return [
              {
                label: '售后管理',
                link: ['extensions/ecommerce/after-sale-list'],
              },
              {
                label: '售后详情',
                link: [''],
              },
            ];
          },
        },
      },
      {
        path: 'subscription-plan',
        pathMatch: 'full',
        component: SubscriptionPlanComponent,
        data: {breadcrumb: '订阅计划'},
      },
      // {
      //   path: 'after-sale-list/:id',
      //   pathMatch: 'full',
      //   component: AfterSaleDetailComponent,
      //   data: {
      //     breadcrumb: () => {
      //       return [
      //         {
      //           label: '售后管理',
      //           link: ['extensions/ecommerce/after-sale-list'],
      //         },
      //         {
      //           label: '售后详情',
      //           link: [''],
      //         },
      //       ];
      //     },
      //   },
      // },
      {
        path: 'channel-address-list',
        pathMatch: 'full',
        component: ChannelAddressListComponent,
        data: {breadcrumb: '地址管理'},
      },

      // 客户管理
      {
        path: 'customers-list',
        pathMatch: 'full',
        component: CustomersListComponent,
        data: {breadcrumb: '客户管理'},
      },
      {
        path: 'customers-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(CustomerResolver),
        component: CustomerDetailComponent,
        data: {
          breadcrumb: detailBreadcrumb('客户管理', '客户列表', 'customers-list'),
        },
      },
      {
        path: 'customer-group-list',
        pathMatch: 'full',
        component: CustomerGroupListComponent,
        data: {breadcrumb: '客户管理'},
      },
      {
        path: 'customer-group-list/:id',
        pathMatch: 'full',
        resolve: {
          detail: (route: ActivatedRouteSnapshot) => {
            return inject(DataService)
              .query(GetCustomerGroupDetailDocument, {id: route.paramMap.get('id') ?? ''})
              .mapStream(data => ({entity: of(data.customerGroup)}));
          },
        },
        component: CustomerGroupDetailComponent,
        data: {
          breadcrumb: detailBreadcrumb('客户管理', '客户列表', 'customer-group-list'),
        },
      },
      {
        path: 'customer-division-list',
        pathMatch: 'full',
        component: CustomerDivisionListComponent,
        data: {breadcrumb: '人群列表'},
      },
      {
        path: 'customer-division-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(CustomerDivisionResolver),
        component: CustomerDivisionDetailComponent,
        data: {breadcrumb: detailBreadcrumb('人群详情', '人群列表', 'customer-division-list')},
      },

      /** 自定义页面 */
      {
        path: 'custom-page-list',
        pathMatch: 'full',
        component: CustomPageListComponent,
        data: {breadcrumb: '自定义页面列表'},
      },
      {
        path: 'custom-page-list/:id',
        pathMatch: 'full',
        component: CustomPageDetailComponent,
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          // breadcrumb: detailBreadcrumb('自定义页面详情', '自定义页面列表', 'custom-page-list'),
          breadcrumb: () => {
            return [
              {
                label: '自定义页面列表',
                link: ['extensions/ecommerce/custom-page-list'],
              },
              {
                label: '自定义页面详情',
                link: [''],
              },
            ];
          },
        },
      },

      /** 热词 */
      {
        path: 'search-hot-words',
        pathMatch: 'full',
        component: SearchHotWordsComponent,
        data: {breadcrumb: '搜索热词管理'},
      },

      /** 公告 */
      {
        path: 'announcement',
        pathMatch: 'full',
        component: AnnouncementComponent,
        data: {breadcrumb: '公告管理'},
      },
      /** 会员卡 */
      {
        path: 'member-card-list',
        pathMatch: 'full',
        component: MemberCardListComponent,
        data: {breadcrumb: '会员卡列表'},
      },
      {
        path: 'member-level-list',
        pathMatch: 'full',
        component: MemberLevelListComponent,
        resolve: createResolveData(MemberLevelResolver),
        data: {breadcrumb: '会员等级'},
      },
      {
        path: 'member-card-list/:id',
        pathMatch: 'full',
        component: MemberCardDetailComponent,
        resolve: createResolveData(MemberCardResolver),
        data: {
          breadcrumb: () => {
            return [
              {
                label: '会员卡列表',
                link: ['extensions/ecommerce/member-card-list'],
              },
              {
                label: '会员卡详情',
                link: [''],
              },
            ];
          },
        },
      },
      {
        path: 'member-card-get-record-list',
        pathMatch: 'full',
        component: MemberCardGetRecordListComponent,
        data: {breadcrumb: '领卡记录'},
      },
      {
        path: 'member-card-get-record-list/grant-card',
        pathMatch: 'full',
        component: MemberCardGrantComponent,
        data: {breadcrumb: '手机号发卡'},
      },
      {
        path: 'member-card-refund-record-list',
        pathMatch: 'full',
        component: MemberCardRefundRecordListComponent,
        data: {breadcrumb: '退卡记录'},
      },
      {
        path: 'gift-card-list',
        pathMatch: 'full',
        component: GiftCardListComponent,
        data: {breadcrumb: '礼品卡列表'},
      },
      {
        path: 'gift-card-list/:id',
        pathMatch: 'full',
        component: GiftCardDetailComponent,
        resolve: createResolveData(GiftCardResolver),
        data: {
          breadcrumb: detailBreadcrumb('礼品卡详情', '礼品卡列表', 'gift-card-list'),
        },
      },
      {
        path: 'gift-card-get-list',
        pathMatch: 'full',
        component: GiftCardGetListComponent,
        data: {
          breadcrumb: [
            {
              label: '礼品卡管理',
              link: [''],
            },
            {
              label: '购买记录',
              link: [''],
            },
          ],
        },
      },
      {
        path: 'gift-card-return-list',
        pathMatch: 'full',
        component: GiftCardRefundListComponent,
        data: {
          breadcrumb: [
            {
              label: '礼品卡管理',
              link: [''],
            },
            {
              label: '退卡记录',
              link: [''],
            },
          ],
        },
      },
      {
        path: 'operation-plan-list',
        pathMatch: 'full',
        component: OperationPlanListComponent,
        data: {breadcrumb: '运营计划列表'},
      },
      {
        path: 'operation-plan-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(OperationPlanResolver),
        component: OperationPlanDetailComponent,
        data: {breadcrumb: detailBreadcrumb('运营计划详情', '运营计划列表', 'operation-plan-list')},
      },
      /** 优惠券 */
      {
        path: 'coupons-list',
        pathMatch: 'full',
        component: CouponsListComponent,
        data: {breadcrumb: '优惠券列表'},
      },
      {
        path: 'coupons-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(CouponResolver),
        component: CouponsDetailComponent,
        data: {
          breadcrumb(_data: Function, params: {id: string; item: string}) {
            const coupon: Coupon | null = params.item ? JSON.parse(params.item) : isNaN(+params.id);

            return [
              {
                label: '优惠券列表',
                link: ['/extensions/ecommerce/coupons-list'],
              },
              {
                label: coupon ? '详情' : '创建',
                link: ['./'],
              },
            ];
          },
        },
      },
      {
        path: 'coupons-received-list',
        pathMatch: 'full',
        component: CouponsReceivedListComponent,
        data: {
          breadcrumb: [
            {
              label: '优惠券列表',
              link: ['extensions/ecommerce/coupons-list'],
            },
            {
              label: '已领取',
              link: [],
            },
          ],
        },
      },
      {
        path: 'coupon-package',
        pathMatch: 'full',
        component: CouponPackageListComponent,
        data: {breadcrumb: '优惠券礼包'},
      },
      {
        path: 'coupon-package/:id',
        pathMatch: 'full',
        resolve: createResolveData(CouponPackageResolver),
        component: CouponPackageDetailComponent,
        data: {breadcrumb: '优惠券礼包'},
      },
      {
        path: 'new-customer-package',
        pathMatch: 'full',
        component: CouponPackageListComponent,
        data: {breadcrumb: '新客授权礼包'},
      },
      {
        path: 'new-customer-package/:id',
        pathMatch: 'full',
        resolve: createResolveData(NewCustomerPackageResolver),
        component: CouponPackageDetailComponent,
        data: {breadcrumb: '新客授权礼包'},
      },
      /** 加价购 */
      {
        path: 'mark-up-buy-list',
        pathMatch: 'full',
        component: MarkUpBuyListComponent,
        data: {breadcrumb: '加价购'},
      },
      {
        path: 'mark-up-buy-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(MarkUpBuyResolver),
        component: MarkUpBuyDetailComponent,
        data: {breadcrumb: detailBreadcrumb('详情', '加价购列表', 'mark-up-buy-list')},
      },
      {
        path: 'distribution/distributor/list',
        pathMatch: 'full',
        component: DistributorListComponent,
        data: {breadcrumb: '分销管理'},
      },
      {
        path: 'distribution/distributor/list/:id',
        pathMatch: 'full',
        component: DistributorCreateComponent,
        data: {
          breadcrumb: [
            {
              label: '分销管理',
              link: ['extensions/ecommerce/distribution/distributor/list'],
            },
            {
              label: '编辑分销员',
              link: [],
            },
          ],
        },
      },
      {
        path: 'distribution/distributor-group/list',
        pathMatch: 'full',
        component: DistributorGroupListComponent,
        data: {breadcrumb: '分销组列表'},
      },
      {
        path: 'distribution/distributor-group/list/:id',
        pathMatch: 'full',
        resolve: createResolveData(DistributorGroupResolver),
        component: DistributorGroupDetailComponent,
        data: {breadcrumb: detailBreadcrumb('分销组详情', '分销组管理', 'distribution/distributor-group/list')},
      },
      {
        path: 'activity-countdown-list',
        pathMatch: 'full',
        component: ActivityCountdownListComponent,
        data: {breadcrumb: '活动倒计时管理'},
      },
      {
        path: 'activity-countdown-list/:id',
        resolve: createResolveData(ActivityCountdownResolver),
        component: ActivityCountdownDetailComponent,
        data: {breadcrumb: detailBreadcrumb('创建活动倒计时', '活动倒计时管理', 'activity-countdown-list')},
      },
      /** 第二件半价 */
      {
        path: 'half-price-list',
        pathMatch: 'full',
        component: HalfPriceListComponent,
        data: {breadcrumb: '第x件x折活动'},
      },
      {
        path: 'half-price-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(HalfPriceResolver),
        component: HalfPriceDetailComponent,
        data: {breadcrumb: detailBreadcrumb('详情', '第x件x折活动', 'half-price-list')},
      },
      {
        path: 'gift-list',
        pathMatch: 'full',
        component: GiftListComponent,
        data: {breadcrumb: '赠品管理列表'},
      },
      // {
      //   path: 'gift-list/:id',
      //   pathMatch: 'full',
      //   resolve: createResolveData(GiftResolver),
      //   component: GiftDetailComponent,
      //   data: {breadcrumb: detailBreadcrumb('详情', '赠品管理列表', 'gift-list')},
      // },
      {
        path: 'gift-list/:id',
        component: ProductDetailComponent,
        resolve: createResolveData(ProductResolver),
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          breadcrumb: detailBreadcrumb('赠品详情', '赠品管理列表', 'gift-list'),
        },
      },
      {
        path: 'gift-list/:id/manage-variants',
        component: ProductVariantsEditorComponent,
        resolve: createResolveData(ProductVariantsResolver),
        canDeactivate: [CanDeactivateDetailGuard],
        data: {
          breadcrumb(_data: () => void, params: {id: string}) {
            return [
              {
                label: '赠品列表',
                link: ['extensions/ecommerce/gift-list'],
              },
              {
                label: '赠品详情',
                link: ['extensions/ecommerce/gift-list', params.id],
              },
              {
                label: '规格管理',
                link: ['./'],
              },
            ];
          },
        },
      },
      {
        path: 'full-gift-list',
        pathMatch: 'full',
        component: FullGiftListComponent,
        data: {breadcrumb: '满减满赠'},
      },
      {
        path: 'full-gift-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(FullGiftResolver),
        component: FullGiftDetailComponent,
        data: {breadcrumb: detailBreadcrumb('详情', '满减满赠', 'full-gift-list')},
      },
      {
        path: 'package-discount-list',
        pathMatch: 'full',
        component: PackageDiscountListComponent,
        data: {breadcrumb: '打包一口价'},
      },
      {
        path: 'package-discount-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(PackageDiscountResolver),
        component: PackageDiscountDetailComponent,
        data: {breadcrumb: detailBreadcrumb('详情', '打包一口价', 'package-discount-list')},
      },
      {
        path: 'member-price-list',
        pathMatch: 'full',
        component: MemberPriceListComponent,
        data: {breadcrumb: '会员价活动'},
      },
      {
        path: 'member-price-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(MemberPriceResolver),
        component: MemberPriceDetailComponent,
        data: {breadcrumb: detailBreadcrumb('创建活动', '会员价活动', 'member-price-list')},
      },
      {
        path: 'optional-discount-list',
        pathMatch: 'full',
        component: OptionalGiftsListComponent,
        data: {breadcrumb: '任选满赠'},
      },
      {
        path: 'optional-discount-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(OptionalGiftsResolver),
        component: OptionalGiftsDetailComponent,
        data: {breadcrumb: '任选满赠'},
      },
      {
        path: 'pay-gift-list',
        pathMatch: 'full',
        component: PayGiftListComponent,
        data: {breadcrumb: '支付有礼'},
      },
      {
        path: 'pay-gift-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(PaymentRewardResolver),
        component: PayGiftDetailComponent,
        data: {breadcrumb: '支付有礼'},
      },
      {
        path: 'exclusion-group-list',
        pathMatch: 'full',
        component: ExclusionGroupListComponent,
        data: {breadcrumb: '低价限购'},
      },
      {
        path: 'exclusion-group-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(ExclusionGroupResolver),
        component: ExclusionGroupDetailComponent,
        data: {breadcrumb: '低价限购'},
      },
      {
        path: 'shopping-credits-grant-list',
        pathMatch: 'full',
        component: ShoppingCreditsGrantListComponent,
        data: {breadcrumb: '购物金发放活动'},
      },
      {
        path: 'shopping-credits-grant-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(ShoppingCreditsGrantResolver),
        component: ShoppingCreditsGrantDetailComponent,
        data: {breadcrumb: '购物金发放活动'},
      },
      {
        path: 'shopping-credits-use-list',
        pathMatch: 'full',
        component: ShoppingCreditsUseListComponent,
        data: {breadcrumb: '购物金抵扣活动'},
      },
      {
        path: 'shopping-credits-use-list/:id',
        pathMatch: 'full',
        resolve: createResolveData(ShoppingCreditsUseResolver),
        component: ShoppingCreditsUseDetailComponent,
        data: {breadcrumb: '购物金抵扣活动'},
      },
      {
        path: 'shopping-credits-config',
        pathMatch: 'full',
        component: ShoppingCreditsConfigComponent,
        data: {breadcrumb: '购物金设置'},
      },
      {
        path: 'general-setting',
        pathMatch: 'full',
        component: GeneralSettingComponent,
        data: {breadcrumb: '通用设置'},
      },
      {
        path: 'float-window-config',
        pathMatch: 'full',
        resolve: createResolveData(FloatingWindowConfigResolver),
        component: FloatWindowConfigComponent,
        data: {breadcrumb: '浮窗设置'},
      },
      {
        path: 'payment-method',
        pathMatch: 'full',
        component: PaymentMethodListComponent,
        data: {breadcrumb: '支付管理'},
      },
      {
        path: 'payment-method/:id',
        pathMatch: 'full',
        resolve: createResolveData(PaymentMethodResolver),
        component: PaymentMethodDetailComponent,
        data: {breadcrumb: '通用设置'},
      },
      {
        path: 'recommend-management',
        pathMatch: 'full',
        component: RecommendManagementComponent,
        data: {breadcrumb: '推荐管理'},
      },
      {
        path: 'share-settings',
        pathMatch: 'full',
        component: ShareSettingsComponent,
        data: {breadcrumb: '分享设置管制'},
      },
      {
        path: 'personal-center-settings',
        pathMatch: 'full',
        component: PersonalCenterSettingsComponent,
        data: {breadcrumb: '个人中心配置'},
      },
      {
        path: 'page-statistics',
        pathMatch: 'full',
        component: PageStatisticsComponent,
        data: {breadcrumb: '页面统计'},
      },
      {
        path: 'data-board',
        pathMatch: 'full',
        component: DataBoardComponent,
        data: {breadcrumb: '数据看板'},
      },
      {
        path: 'product-insights',
        pathMatch: 'full',
        component: ProductInsightsComponent,
        data: {breadcrumb: '商品洞察'},
      },
      {
        path: 'customer-trade',
        pathMatch: 'full',
        component: CustomerTradeComponent,
        data: {breadcrumb: '客户交易'},
      },
      {
        path: 'channel-list',
        pathMatch: 'full',
        component: ChannelListComponent,
        data: {breadcrumb: '销售渠道', locationId: 'channel-list'},
      },
      {
        path: 'channel-list/:id',
        pathMatch: 'full',
        component: ChannelDetailComponent,
        resolve: {
          detail: (route: ActivatedRouteSnapshot) => {
            return inject(DataService)
              .query(GetChannelDetailDocument, {id: route.paramMap.get('id') ?? ''})
              .mapStream(data => ({entity: of(data.channel)}));
          },
        },
        data: {breadcrumb: '销售渠道'},
      },
      {
        path: 'points-shop-list',
        pathMatch: 'full',
        component: PointsProductListComponent,
        data: {breadcrumb: '积分商品管理'},
      },
      {
        path: 'points-shop-list/:id',
        pathMatch: 'full',
        component: PointsProductDetailComponent,
        resolve: createResolveData(PointsProductResolver),
        data: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          breadcrumb: (_data: any, params: {id: string}) => {
            return _data.entity.pipe(
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              map((entity: any) => {
                return [
                  {
                    label: '积分商品管理',
                    link: ['extensions/ecommerce/points-shop-list'],
                  },
                  {
                    label: `${entity?.product?.name ? entity.product.name : '新建兑换活动'}`,
                    link: ['./'],
                  },
                ];
              }),
            );
          },
        },
      },
      {
        path: 'points-shop-rule',
        pathMatch: 'full',
        component: PointsConfigComponent,
        data: {breadcrumb: '积分规则管理'},
      },
      {
        path: 'check-in-statistics',
        pathMatch: 'full',
        component: CheckInStatisticsComponent,
        data: {breadcrumb: '签到数据统计'},
      },
      {
        path: 'check-in-config',
        pathMatch: 'full',
        component: CheckInConfigComponent,
        data: {breadcrumb: '签到规则设置'},
      },
      {
        path: 'check-in-statistics/customer-list',
        pathMatch: 'full',
        component: CheckInCustomerListComponent,
        data: {
          breadcrumb: [
            {
              label: '签到数据统计',
              link: ['extensions/ecommerce/check-in-statistics'],
            },
            {
              label: '连签用户查询',
              link: [],
            },
          ],
        },
      },
      {
        path: 'blind-box-config',
        pathMatch: 'full',
        component: BlindBoxConfigComponent,
        resolve: createResolveData(BlindBoxConfigResolver),
        data: {breadcrumb: '盲盒通用设置'},
      },
      {
        path: 'blind-box-statistics',
        pathMatch: 'full',
        component: BlindBoxStatisticsComponent,
        data: {breadcrumb: '盲盒数据统计'},
      },
      {
        path: 'blind-box-list',
        pathMatch: 'full',
        component: BlindBoxListComponent,
        data: {breadcrumb: '盲盒列表'},
      },
      {
        path: 'blind-box-list/:id',
        pathMatch: 'full',
        component: BlindBoxDetailComponent,
        resolve: createResolveData(BlindBoxResolver),
        data: {breadcrumb: detailBreadcrumb('创建盲盒', '盲盒列表', 'blind-box-list')},
      },
      {
        path: 'blind-box-activity-list',
        pathMatch: 'full',
        component: BlindBoxActivityListComponent,
        data: {breadcrumb: '盲盒活动列表'},
      },
      {
        path: 'blind-box-activity-list/:id',
        pathMatch: 'full',
        component: BlindBoxActivityDetailComponent,
        resolve: createResolveData(BlindBoxActivityResolver),
        data: {breadcrumb: detailBreadcrumb('创建盲盒活动', '盲盒活动列表', 'blind-box-activity-list')},
      },
      {
        path: 'blind-box-open-record-list',
        pathMatch: 'full',
        component: BlindBoxOpenRecordListComponent,
        data: {breadcrumb: '开盒记录列表'},
      },
      {
        path: 'blind-box-open-record-list/:id',
        pathMatch: 'full',
        component: BlindBoxOpenRecordDetailComponent,
        resolve: createResolveData(BlindBoxOpenRecordResolver),
        data: {breadcrumb: detailBreadcrumb('开盒记录详情', '开盒记录列表', 'blind-box-open-record-list')},
      },
      {
        path: 'channel-post-list',
        pathMatch: 'full',
        component: ChannelPostListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '平台帖子管理',
              link: [],
            },
          ],
        },
      },
      {
        path: 'channel-post-list/:id',
        pathMatch: 'full',
        component: ChannelPostDetailComponent,
        resolve: createResolveData(ChannelPostResolver),
        data: {
          breadcrumb: detailBreadcrumb('帖子创建', '平台帖子管理', 'channel-post-list', 'title', [
            {label: '心愿笔记', link: []},
          ]),
        },
      },
      {
        path: 'customer-post-list',
        pathMatch: 'full',
        component: CustomerPostListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '用户帖子管理',
              link: [],
            },
          ],
        },
      },
      {
        path: 'post-detail/:id',
        pathMatch: 'full',
        component: PostDetailComponent,
        resolve: createResolveData(PostResolver),
        data: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          breadcrumb: (_data: any, params: {id: string}) => {
            return _data?.entity?.pipe(
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              map((entity: any) => {
                const isWish = entity?.isWish;
                return [
                  {
                    label: '心愿笔记',
                    link: ['./'],
                  },
                  {
                    label: isWish ? '平台帖子管理' : '用户帖子管理',
                    link: ['extensions/ecommerce/' + (isWish ? 'channel-post-list' : 'customer-post-list')],
                  },
                  {
                    label: `${entity?.title ? entity.title : '帖子详情'}`,
                    link: ['./'],
                  },
                ];
              }),
            );
          },
        },
      },
      {
        path: 'note-activity-list',
        pathMatch: 'full',
        component: ForumActivityListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '活动管理',
              link: [],
            },
          ],
        },
      },
      {
        path: 'note-activity-list/:id',
        pathMatch: 'full',
        component: ForumActivityDetailComponent,
        resolve: createResolveData(ForumActivityResolver),
        data: {
          breadcrumb: detailBreadcrumb('活动创建', '活动管理', 'note-activity-list', 'name', [
            {label: '心愿笔记', link: []},
          ]),
        },
      },
      {
        path: 'forum-activity-rank-list',
        pathMatch: 'full',
        component: ForumActivityRankComponent,
        data: {
          // breadcrumb: detailBreadcrumb(
          //   '活动详情',
          //   '活动管理',
          //   'note-activity-list',
          //   'name',
          //   [
          //     {
          //       label: '心愿笔记',
          //       link: [],
          //     },
          //   ],
          //   [
          //     {
          //       label: '查看排名',
          //       link: [],
          //     },
          //   ],
          // ),
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '查看排名',
              link: [],
            },
          ],
        },
      },
      {
        path: 'topic-management-list',
        pathMatch: 'full',
        component: ForumTagListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '话题管理',
              link: [],
            },
          ],
        },
      },
      {
        path: 'topic-management-list/:id',
        pathMatch: 'full',
        component: ForumTagDetailComponent,
        resolve: createResolveData(ForumTagResolver),
        data: {
          breadcrumb: detailBreadcrumb('话题创建', '话题管理', 'topic-management-list', 'name', [
            {
              label: '心愿笔记',
              link: [],
            },
          ]),
        },
      },
      {
        path: 'post-comment-list',
        pathMatch: 'full',
        component: PostCommentListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '评论管理',
              link: [],
            },
          ],
        },
      },
      {
        path: 'post-comment-list/:id',
        pathMatch: 'full',
        component: PostCommentDetailComponent,
        resolve: createResolveData(PostCommentResolver),
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '评论管理',
              link: ['extensions/ecommerce/post-comment-list'],
            },
            {
              label: '评论详情',
              link: [],
            },
          ],
        },
      },

      {
        path: 'forum-customer-list',
        pathMatch: 'full',
        component: ForumCustomerListComponent,
        data: {
          breadcrumb: [
            {
              label: '心愿笔记',
              link: [],
            },
            {
              label: '发文账号管理',
              link: [],
            },
          ],
        },
      },
    ]),
  ],
  providers: [],
  declarations: [
    ...COMPS,
    OptionalGiftsListComponent,
    OptionalGiftsDetailComponent,
    GiftSelectDialogComponent,
    PayGiftListComponent,
    PayGiftDetailComponent,
  ],
  exports: [CommonModule],
})
export class EcommerceUICommonModule {}

function detailBreadcrumb(
  name: string,
  lastName = '上一级',
  lastPath = '../',
  keyName = 'name',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  preset: {label: string; link: any}[] = [],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  suffix: {label: string; link: any}[] = [],
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (_data: any, params: {id: string}) => {
    // inject(DataService).client.uiState();
    // return [
    //   {
    //     label: lastName,
    //     link: ['extensions/ecommerce/' + lastPath],
    //   },
    //   {
    //     label: isNaN(+params.id) ? '创建' : name,
    //     link: ['./'],
    //   },
    // ];
    return _data?.entity?.pipe(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      map((entity: any) => [
        ...preset,
        {
          label: lastName,
          link: ['extensions/ecommerce/' + lastPath],
        },
        {
          label: `${entity?.[keyName] ? entity[keyName] : name}`,
          link: ['./'],
        },
        ...suffix,
      ]),
    );
  };
}
